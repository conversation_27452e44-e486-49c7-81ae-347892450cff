<script setup lang="ts">
// RechargeCard 已移动到组件库，现在使用全局注册的 ZAccountCard
import type { FormInstance } from "vant";
import { showToast } from "vant";
import { isPhilippinePhoneNumber } from "@/utils/core/tools";
import { addWithdrawAccount, updateWithdrawAccount } from "@/api/withdrawal";
import { useRoute } from "vue-router";
import router from "@/router";
import { ref, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { getLocalStorage } from "@/utils/core/Storage";
import { useWithdrawStore } from "@/stores/withdraw";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import VerifyDialogWithdrawAccount from "@/components/ZVerifyDialog/VerifyDialogWithdrawAccount.vue";
import { PN_VERIFY_TYPE } from "@/components/ZVerifyDialog/types";
import { useGlobalStore } from "@/stores/global";
import { getGlobalDialog } from "@/enter/vant";

const withdrawStore = useWithdrawStore();
const globalStore = useGlobalStore();
const { withdrawData } = storeToRefs(withdrawStore);
const showVerifyDialogWithdrawAccount = ref(false);

// 变量与类型
const route = useRoute();
const isEditMode = computed(() => route.query.type === "edit");
const curUpdateAccountInfo = ref<Record<string, any>>(
  getLocalStorage("withdrawAccountSelected") || {}
);

const formRef = ref<FormInstance>();
const showDialog = ref(false);
const selectedAccountType = ref();
const selectedAccountName = ref();
const editWithdrawAccountType = ref(PN_VERIFY_TYPE.AddWithdrawAccount);

const formData = ref({
  accountNo: "",
  firstName: "",
  middleName: "",
  lastName: "",
});

const isMayaOrGcash = computed(() => {
  return (
    selectedAccountName.value === CHANEL_TYPE.MAYA ||
    selectedAccountName.value === CHANEL_TYPE.G_CASH
  );
});

// 提现渠道
const getRechargeWithdraw = async () => {
  const res = await withdrawStore.getWithdrawConfigList();
  selectedAccountType.value = res?.[0]?.account_type;
};

// 选中渠道
const handleCheck = (data) => {
  selectedAccountType.value = data.account_type;
};

// 校验手机号
const validatePhoneNumber = (value: string) => isPhilippinePhoneNumber(value);

// 格式化手机号
const formatPhoneNumber = (value) => {
  if (value.length >= 2 && !value.startsWith("09")) {
    value = "09" + value.slice(2);
  }
  value = value.slice(0, 11);
  formData.value.accountNo = value;
};

// 表单提交逻辑
const onSubmit = async () => {
  await formRef.value?.validate();
  showDialog.value = true;
  if (isEditMode.value) {
    editWithdrawAccountType.value = PN_VERIFY_TYPE.ChangeWithdrawAccount;
  } else {
    editWithdrawAccountType.value = PN_VERIFY_TYPE.AddWithdrawAccount;
  }
};

const handleResult = (res) => {
  if (res.code && (res.code == 0 || res.code == 200)) {
    showToast("operate successfully");
    showVerifyDialogWithdrawAccount.value = false;
    router.back();
  } else if (res.code == 200003) {
    getGlobalDialog()({
      message: res.msg,
      showCancelButton: false,
    });
  } else {
    showToast(res.msg);
  }
};

const handleCheckAccount = () => {
  showDialog.value = false;
  if ("0" + globalStore.userInfo.phone === formData.value.accountNo) {
    handleSubmit();
  } else {
    showVerifyDialogWithdrawAccount.value = true;
  }
};

const handleSubmit = async () => {
  const { accountNo, firstName, middleName, lastName } = formData.value;
  const params = {
    account_no: accountNo,
    first_name: firstName,
    middle_name: middleName,
    last_name: lastName,
    type: selectedAccountType.value,
  };

  if (isEditMode.value && curUpdateAccountInfo.value.account_id) {
    const res = await updateWithdrawAccount({
      ...params,
      account_id: curUpdateAccountInfo.value.account_id,
    });
    handleResult(res);
  } else {
    const res = await addWithdrawAccount({
      ...params,
    });
    handleResult(res);
  }
};

// 按钮可用性
const vaildBtn = computed(() => !!formData.value.accountNo);

// 监听编辑模式和渠道列表变化，自动填充表单
watch(
  [() => route.query.type, () => withdrawData.value],
  ([type, newAccountList]) => {
    if (type === "edit" && newAccountList.length) {
      const Info = curUpdateAccountInfo.value;
      console.log(1111, Info);
      formData.value.accountNo = Info.account_no || "";
      selectedAccountType.value = Info.type;
      const selectedAccount = newAccountList.find((item) => item.account_type == Info.type);
      selectedAccountName.value = selectedAccount?.name;
      console.log(2222, selectedAccount);
      if (!isMayaOrGcash.value) {
        formData.value.firstName = Info.first_name || "";
        formData.value.middleName = Info.middle_name || "";
        formData.value.lastName = Info.last_name || "";
      }
    }
  },
  { immediate: true }
);
</script>

<template>
  <ZPage :request="getRechargeWithdraw">
    <div class="content">
      <div class="simple-card">
        <template v-for="item in withdrawData" :key="item.id">
          <ZAccountCard
            :checkOffset="8"
            :cardHeight="68"
            :item="item"
            :selectedId="selectedAccountType"
            class="card-item"
            @click="handleCheck"
          />
        </template>
      </div>
      <div class="account-form">
        <van-form ref="formRef">
          <ZFormField
            labelAlias="Account number"
            v-model="formData.accountNo"
            :maxLength="11"
            type="digit"
            placeholder="Enter your account number"
            :rules="[{ validator: validatePhoneNumber, message: 'Wrong phone number' }]"
            @input="formatPhoneNumber"
          >
            <template #label>
              <span>{{ selectedAccountName }} Account No.</span>
              <span class="text-999"> (09xx xxxx xxx)</span>
            </template>
          </ZFormField>
          <ZFormField
            v-if="!isMayaOrGcash"
            label="First Name"
            v-model="formData.firstName"
            placeholder="Please enter first name"
            restriction="alphabetic"
            :maxLength="50"
          ></ZFormField>
          <ZFormField
            v-if="!isMayaOrGcash"
            label="Middle Name"
            v-model="formData.middleName"
            :required="false"
            placeholder="Please enter middle name."
            restriction="alphabetic"
            :maxLength="50"
          ></ZFormField>
          <ZFormField
            v-if="!isMayaOrGcash"
            label="Last Name"
            v-model="formData.lastName"
            placeholder="Please enter last name"
            restriction="alphabetic"
            :maxLength="50"
          ></ZFormField>
        </van-form>
      </div>
    </div>
    <div class="footer">
      <div class="submit-btn">
        <ZButton type="primary" @click="onSubmit" :disabled="!vaildBtn">
          Submit
          <!-- <ZIcon type="icon-duihao" color="#fff"></ZIcon> -->
        </ZButton>
      </div>
    </div>
    <ZDialog
      v-model="showDialog"
      title="Account Confirmation"
      :onConfirm="handleCheckAccount"
      class="dialog"
    >
      <p class="describe">
        Please confirm the {{ selectedAccountName }} account information below is accurate:
      </p>
      <p class="label" v-if="!isMayaOrGcash">
        First Name: <span class="value">{{ formData.firstName }}</span>
      </p>
      <p class="label" v-if="!isMayaOrGcash">
        Middle Name: <span class="value">{{ formData.middleName }}</span>
      </p>
      <p class="label" v-if="!isMayaOrGcash">
        Last Name: <span class="value">{{ formData.lastName }}</span>
      </p>
      <p class="label">
        {{ selectedAccountName }} Account No.: <span class="value">{{ formData.accountNo }}</span>
      </p>
      <p class="tips">
        <ZIcon type="icon-warn" class="tips_icon" color="" :size="14"></ZIcon>
        The platform is not responsible for any losses incurred due to incorrect account information
        provided by the customer.
      </p>
    </ZDialog>
    <!-- 添加、编辑账户 -->
    <VerifyDialogWithdrawAccount
      v-model:showDialog="showVerifyDialogWithdrawAccount"
      :verifyType="editWithdrawAccountType"
      :succCallBack="handleSubmit"
    />
  </ZPage>
</template>

<style lang="scss" scoped>
.content {
  padding: 16px;
  padding-bottom: 120px;
}
.text-999 {
  color: #999;
}

.simple-card {
  display: flex;
  justify-content: space-between;
  gap: 13px;
  overflow: auto;
  flex-wrap: wrap;
  padding-bottom: 28px;
  .card-item {
    width: 48%;
  }
}

.account-form {
  &:deep(.z-form-field) {
    margin-bottom: 20px;
    .field-input .van-field__control {
      font-family: "Inter";
      font-size: 16px;
    }
  }

  .required {
    color: #ff4d4f;
    margin-right: 4px;
  }

  .form-label {
    margin-bottom: 12px;
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
  }

  .form-input {
    --van-field-border: none;
    /* 移除 Vant Field 默认底部边框 */
    --van-field-padding: 8px 0;
    /* 调整内边距，适配独占一行 */
    background-color: #f7f8fa;
    /* 背景色示例，可根据设计调整 */
    border-radius: 999px;
    display: inline-flex;
    // height: 48px;
    padding: 12px 20px;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
    color: #222;
    font-family: D-DIN;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: -0.3px;
  }
}

.footer {
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  text-align: center;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  .submit-btn {
    width: 335px;
    height: 52px;

    /*  .icon-duihao {
      font-size: 28px;
    } */
  }
}

.dialog {
  .describe {
    color: #999;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    /* 171.429% */
  }

  .label {
    color: #999;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
    /* 228.571% */
  }

  .value {
    color: #222;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
  }

  .tips {
    margin-top: 12px;
    border-radius: 8px;
    padding: 8px 12px;
    background: var(---, #fffaf8);
    color: #ff936f;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
  }
}
</style>

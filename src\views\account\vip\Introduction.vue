<template>
  <XPage :navBarStyle="{ background: '#fff', color: '#333' }" @back.stop.prevent="goBack">
    <div class="page-vipIntro">
      <div class="vip-introduction">
        <div class="top-tip">
          <span>VIP</span>
          <span class="top-tip-img"></span>
        </div>

        <!-- VIP Benefits Section -->
        <div class="vip-benefits-section">
          <h2 class="section-title">VIP Benefits</h2>
          <div class="benefit-list">
            <div class="benefit-item">
              <div class="benefit-icon">
                <span class="iconfont icon-tongban"></span>
              </div>
              <div class="benefit-content">
                <div class="benefit-title">Highest cashback {{ bonusList[0].rate }}%</div>
                <div class="benefit-desc">
                  Exclusive activity rules, higher cashback.<br />
                  No other conditions are required to directly trigger the top cashback
                </div>
              </div>
            </div>
            <div class="benefit-item">
              <div class="benefit-icon">
                <span class="iconfont icon-kefu1"></span>
              </div>
              <div class="benefit-content">
                <div class="benefit-title">Dedicated Support Channels</div>
                <div class="benefit-desc">
                  Exclusive activity rules, higher cashback.<br />
                  No other conditions are required to directly trigger the top cashback
                </div>
              </div>
            </div>
            <div class="benefit-item">
              <div class="benefit-icon">
                <span class="iconfont icon-qianbao"></span>
              </div>
              <div class="benefit-content">
                <div class="benefit-title">Priority Withdrawals</div>
                <div class="benefit-desc">
                  Exclusive activity rules, higher cashback.<br />
                  No other conditions are required to directly trigger the top cashback
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- How to become VIP Section -->
        <div class="vip-howto-section">
          <h2 class="section-title">How to become VIP</h2>
          <div class="howto-card">
            <ul>
              <li>
                Accumulate a total betting amount exceeding ₱250,000 within a half-month period to
                qualify as a VIP. The first half-month runs from the 1st to the 15th, and the second
                half-month runs from the 16th to the end of the month.
              </li>
              <li>
                To maintain VIP status, users must accumulate a total betting amount exceeding
                ₱300,000 within each half-month period.
              </li>
              <li>
                Users who meet the betting criteria will be upgraded to VIP automatically by the 1st
                and 16th of each month. Users who do not meet the criteria will be downgraded
                automatically.
              </li>
              <li>All platform registered users are eligible to participate in this campaign.</li>
            </ul>
          </div>
        </div>

        <!-- Unlimited BONUS Table Section -->
        <div class="vip-bonus-section">
          <h2 class="section-title">Maglaro para sa Unlimited BONUS (VIP)</h2>

          <!-- table head -->
          <div class="bonus-table-header">
            <div class="bonus-table-header-th">Daily BetAmount</div>
            <div class="bonus-table-header-th">Cashback</div>
          </div>
          <!-- table body -->
          <div class="bonus-table-body" v-for="item in bonusList" :key="item.game_type">
            <div class="bonus-table-body-td">{{ item.game_type }} rebate ratio</div>
            <div class="bonus-table-body-td">{{ item.rate }}%</div>
          </div>
        </div>

        <!-- Other Notices Section -->
        <div class="vip-other-notices-section">
          <h2 class="section-title">Other Notices</h2>
          <div class="other-notices-card">
            <ul>
              <li>
                During the activity period, if any irregularities are discovered, Platform reserves
                the right to disqualify participants and recover related benefits.
              </li>
              <li>
                Platform reserves the right to adjust the activity rules, with any adjustments being
                announced in advance on the activity page.
              </li>
              <li>Platform reserves the right for the final interpretation of the campaign.</li>
              <li>
                Participation in this activity implies agreement to and compliance with the above
                rules. For any questions, please feel free to contact our customer service team.
              </li>
            </ul>
          </div>
        </div>
      </div>
      <ZFootPng />
    </div>
  </XPage>
</template>

<script setup>
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { rebateConf } from "@/api/user";

const router = useRouter();
const route = useRoute();

const bonusList = ref([]);

// 字符串处理：首字母大写其余小写
function toTitleCase(str) {
  return str.toLowerCase().replace(/\b\w/g, function (char) {
    return char.toUpperCase();
  });
}

const getData = () => {
  rebateConf().then((res) => {
    // 如果返回的配置列表不为空，则更新 bonusList
    if (res.config.length > 0) {
      const resData = [...res.config[0]];
      resData.shift();
      bonusList.value = resData.map((item) => {
        return {
          game_type: toTitleCase(item.game_type),
          rate: item.rate,
        };
      });
    }
  });
};

const goBack = () => {
  if (route.query.returnTo) {
    router.replace(route.query.returnTo);
  } else {
    router.back();
  }
};

onBeforeMount(() => {
  getData();
});
</script>

<style lang="scss" scoped>
.page-vipIntro {
  background: #291403;
  min-height: 100%;
  padding-top: 30px;
  color: #fff;
}

.vip-introduction {
  background-color: #291403;
  width: 100%;
  min-height: 100vh;
  padding: 20px 16px 0;
}

.top-tip {
  display: flex;
  align-items: baseline;
  justify-content: space-around;
  background-image: url("../../../assets/images/vip-tip-bg.png");
  background-repeat: no-repeat;
  background-size: contain;
  height: 150px;
  background-position: left 0 top 2.667vw;

  .top-tip-img {
    background-image: url("../../../assets/images/vip-tip.png");
    background-repeat: no-repeat;
    background-size: cover;
    width: 37.533vw;
    height: 32.733vw;
    position: relative;
    right: -30px;
    top: -20px;
  }

  span:first-child {
    position: relative;
    top: -4vw;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: 700;
    font-size: 46px;
    color: #9f5b00;
  }
}

.section-title {
  color: #fff3d6;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 14px;
  margin-top: 0;
  text-align: center;
  letter-spacing: 0.5px;
}

.vip-benefits-section {
  margin: 12px 0 24px 0;

  .benefit-list {
    background: #3a2512;
    border-radius: 18px;
    padding: 18px 0;
    display: flex;
    flex-direction: column;
    gap: 18px;
  }

  .benefit-item {
    display: flex;
    align-items: flex-start;
    padding: 0 18px;
    gap: 14px;
  }

  .benefit-icon {
    width: 48px;
    height: 48px;
    background: #6b5437;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2px;

    .iconfont {
      background: linear-gradient(to right, #fff7e1, #fcd898);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
      font-size: 28px;
      border-radius: 50%;
    }
  }

  .benefit-content {
    flex: 1;

    .benefit-title {
      color: #fff7e1;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .benefit-desc {
      color: #fff;
      font-size: 13px;
      line-height: 1.5;
    }
  }
}

.vip-howto-section {
  margin: 0 0 24px 0;

  .howto-card {
    background: #3a2512;
    border-radius: 18px;
    padding: 16px;
    color: #fff;
    font-size: 14px;

    ul {
      list-style: auto;
      margin: 0;
      padding-left: 18px;

      li {
        margin-bottom: 10px;
        line-height: 1.6;
      }
    }
  }
}

.vip-bonus-section {
  margin: 0 0 24px 0;

  .bonus-table-header,
  .bonus-table-body {
    display: flex;
    height: 48px;
    color: #fff8ed;

    &-th,
    &-td {
      height: 100%;
      width: 100%;
      line-height: 48px;
      border: 0.5px solid #906543;
      text-align: center;
      width: 50%;
    }
  }

  .bonus-table-header {
    background-color: #3a2412;
    border: 0.5px solid #906543;
    border-radius: 20px 20px 0 0;
    font-weight: 600;
  }

  .bonus-table-header-th:first-child {
    border-radius: 20px 0 0 0;
  }

  .bonus-table-header-th:last-child {
    border-radius: 0 20px 0 0;
  }

  .bonus-table-body {
    background-color: #4d3420;
  }

  .bonus-table-body:last-child {
    border-radius: 0 0 20px 20px;

    .bonus-table-body-td:first-child {
      border-radius: 0 0 0 20px;
    }

    .bonus-table-body-td:last-child {
      border-radius: 0 0 20px 0;
    }
  }
}

.vip-other-notices-section {
  margin: 0 0 24px 0;

  .other-notices-card {
    background: #3a2512;
    border-radius: 18px;
    padding: 14px;
    color: #fff;
    font-size: 14px;

    ul {
      list-style: auto;
      margin: 0;
      padding-left: 12px;

      li {
        margin-bottom: 10px;
        line-height: 1.6;
      }
    }
  }
}
</style>
